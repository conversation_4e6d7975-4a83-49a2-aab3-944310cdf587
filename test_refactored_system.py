#!/usr/bin/env python3
"""
Test script for the refactored SOC Jira Tracker system
This script validates the core functionality without running full exports
"""

import os
import sys
import json
import logging
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from client_config import ClientConfig
from jira_main import (
    setup_logging, Config, ensure_export_directory, 
    validate_jira_config, validate_and_show_client_config
)

def test_logging_setup():
    """Test logging configuration"""
    print("Testing logging setup...")
    try:
        logger = setup_logging()
        logger.info("Logging test successful")
        print("✓ Logging setup working")
        return True
    except Exception as e:
        print(f"✗ Logging setup failed: {e}")
        return False

def test_environment_config():
    """Test environment configuration"""
    print("Testing environment configuration...")
    try:
        # This will validate required environment variables
        config = Config()
        print("✓ Environment configuration valid")
        return True
    except Exception as e:
        print(f"✗ Environment configuration failed: {e}")
        print("  Make sure your app.env file has the required variables:")
        print("  - JIRA_USERNAME1")
        print("  - JIRA_SERVER") 
        print("  - JIRA_TOKEN")
        return False

def test_export_directory():
    """Test export directory creation"""
    print("Testing export directory creation...")
    try:
        export_dir = ensure_export_directory()
        if export_dir.exists():
            print(f"✓ Export directory created: {export_dir.absolute()}")
            return True
        else:
            print("✗ Export directory was not created")
            return False
    except Exception as e:
        print(f"✗ Export directory creation failed: {e}")
        return False

def test_jira_config_validation():
    """Test Jira configuration validation"""
    print("Testing Jira configuration validation...")
    try:
        jira_config = validate_jira_config()
        print("✓ Jira configuration file is valid")
        return True
    except FileNotFoundError:
        print("✗ jira_one_config.json file not found")
        print("  Create this file with your Jira authentication details")
        return False
    except Exception as e:
        print(f"✗ Jira configuration validation failed: {e}")
        return False

def test_client_configuration():
    """Test client configuration"""
    print("Testing client configuration...")
    try:
        # Test basic client operations
        all_clients = ClientConfig.get_all_clients()
        msoc_clients = ClientConfig.get_msoc_clients()
        safe_clients = ClientConfig.get_safe_msoc_clients()
        
        print(f"  Total clients configured: {len(all_clients)}")
        print(f"  MSOC clients: {len(msoc_clients)}")
        print(f"  Safe MSOC clients: {len(safe_clients)}")
        
        # Test JQL generation
        jql_list = ClientConfig.get_jql_client_list(safe_clients)
        print(f"  JQL client list length: {len(jql_list)}")
        
        # Test validation
        validation = ClientConfig.validate_client_list(safe_clients)
        if validation['invalid']:
            print(f"  Warning: Invalid clients found: {validation['invalid']}")
        
        print("✓ Client configuration working")
        return True
    except Exception as e:
        print(f"✗ Client configuration failed: {e}")
        return False

def test_system_validation():
    """Test overall system validation"""
    print("Testing system validation...")
    try:
        config_valid = validate_and_show_client_config()
        if config_valid:
            print("✓ System validation passed")
            return True
        else:
            print("✗ System validation failed")
            return False
    except Exception as e:
        print(f"✗ System validation error: {e}")
        return False

def main():
    """Run all tests"""
    print("=== SOC Jira Tracker - System Validation Tests ===\n")
    
    tests = [
        ("Logging Setup", test_logging_setup),
        ("Environment Config", test_environment_config),
        ("Export Directory", test_export_directory),
        ("Jira Config", test_jira_config_validation),
        ("Client Configuration", test_client_configuration),
        ("System Validation", test_system_validation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {len(results)} tests")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed! Your system is ready to run.")
        return 0
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please fix the issues before running the main system.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
