"""
PDRM Firewall Data Formatter

This module provides functions to format and structure PDRM Firewall ticket data
for Jira automation. It handles CSV and JSON output formats with proper field
ordering and formula placeholders for derived values.
"""

import csv
import json
import pandas as pd
from typing import List, Dict, Any, Optional
from datetime import datetime
from client_config import PDRMFirewallDataStructure


class PDRMFirewallFormatter:
    """
    Formatter class for PDRM Firewall ticket data
    """
    
    def __init__(self):
        self.data_structure = PDRMFirewallDataStructure()
        self.field_order = self.data_structure.get_field_order()
        self.jira_mapping = self.data_structure.get_jira_mapping()
        self.derived_fields = self.data_structure.get_derived_fields()
    
    def transform_jira_data_to_pdrm_format(self, jira_data: List[Dict]) -> List[Dict]:
        """
        Transform Jira export data to PDRM Firewall format
        
        Args:
            jira_data: List of dictionaries containing Jira ticket data
            
        Returns:
            List of dictionaries in PDRM Firewall format
        """
        transformed_data = []
        
        for ticket in jira_data:
            pdrm_ticket = {}
            
            # Map Jira fields to PDRM format
            for pdrm_field, jira_field in self.jira_mapping.items():
                pdrm_ticket[pdrm_field] = ticket.get(jira_field, "")
            
            # Add formula placeholders for derived fields
            for field, formula in self.derived_fields.items():
                if field in pdrm_ticket:
                    pdrm_ticket[field] = formula
            
            transformed_data.append(pdrm_ticket)
        
        return transformed_data
    
    def create_csv_template(self, output_file: str, include_sample_data: bool = False) -> None:
        """
        Create a CSV template with proper field ordering
        
        Args:
            output_file: Path to output CSV file
            include_sample_data: Whether to include sample data row
        """
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=self.field_order)
            writer.writeheader()
            
            if include_sample_data:
                sample_row = self._create_sample_data()
                writer.writerow(sample_row)
    
    def export_to_csv(self, data: List[Dict], output_file: str) -> None:
        """
        Export PDRM Firewall data to CSV format
        
        Args:
            data: List of PDRM Firewall ticket dictionaries
            output_file: Path to output CSV file
        """
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=self.field_order)
            writer.writeheader()
            
            for row in data:
                # Ensure all required fields are present
                ordered_row = {}
                for field in self.field_order:
                    ordered_row[field] = row.get(field, "")
                writer.writerow(ordered_row)
    
    def export_to_json(self, data: List[Dict], output_file: str, pretty_print: bool = True) -> None:
        """
        Export PDRM Firewall data to JSON format
        
        Args:
            data: List of PDRM Firewall ticket dictionaries
            output_file: Path to output JSON file
            pretty_print: Whether to format JSON with indentation
        """
        # Ensure proper field ordering in JSON
        ordered_data = []
        for row in data:
            ordered_row = {}
            for field in self.field_order:
                ordered_row[field] = row.get(field, "")
            ordered_data.append(ordered_row)
        
        with open(output_file, 'w', encoding='utf-8') as jsonfile:
            if pretty_print:
                json.dump(ordered_data, jsonfile, indent=2, ensure_ascii=False)
            else:
                json.dump(ordered_data, jsonfile, ensure_ascii=False)
    
    def validate_data(self, data: List[Dict]) -> Dict[str, Any]:
        """
        Validate PDRM Firewall data structure
        
        Args:
            data: List of ticket dictionaries to validate
            
        Returns:
            Dictionary with validation results
        """
        validation_results = {
            'valid': True,
            'total_records': len(data),
            'errors': [],
            'warnings': []
        }
        
        for i, record in enumerate(data):
            record_validation = self.data_structure.validate_data_structure(record)
            
            if record_validation['missing']:
                validation_results['valid'] = False
                validation_results['errors'].append({
                    'record': i + 1,
                    'type': 'missing_fields',
                    'fields': record_validation['missing']
                })
            
            if record_validation['extra']:
                validation_results['warnings'].append({
                    'record': i + 1,
                    'type': 'extra_fields',
                    'fields': record_validation['extra']
                })
        
        return validation_results
    
    def _create_sample_data(self) -> Dict[str, str]:
        """Create sample data for template"""
        sample_data = {
            'issue_key': 'MSOC-XXXXX',
            'summary': '[PDRM][MSOC-XXXXX] Sample Firewall Issue',
            'pdrm_fault_category': 'Network Connectivity',
            'closing_time': '2025-01-15 14:30:00',
            'pdrm_acknowledge_venue': 'IPD Sample Location',
            'tm_pdrm_holding_time': '=closing_time - pdrm_acknowledge_time',
            'total_time_taken': '=closing_time - issue_created_time',
            'pdrm_acknowledge_time': '2025-01-15 10:15:00',
            'pdrm_tl_holding_time': '=pdrm_acknowledge_time - tl_start_time',
            'states': 'Resolved',
            'comment': 'Sample analyst comment',
            'analyst_name': 'Sample Analyst'
        }
        return sample_data
    
    def get_field_descriptions(self) -> Dict[str, str]:
        """Get descriptions for each field"""
        descriptions = {
            'issue_key': 'Jira issue ID (e.g., MSOC-12345)',
            'summary': 'Short description of the issue',
            'pdrm_fault_category': 'Classification of the fault type',
            'closing_time': 'Timestamp when the ticket was closed',
            'pdrm_acknowledge_venue': 'Venue acknowledged by PDRM',
            'tm_pdrm_holding_time': 'Derived: closing_time - pdrm_acknowledge_time',
            'total_time_taken': 'Derived: closing_time - issue_created_time',
            'pdrm_acknowledge_time': 'Timestamp when PDRM acknowledged',
            'pdrm_tl_holding_time': 'Derived: pdrm_acknowledge_time - tl_start_time',
            'states': 'Current state or location of the issue',
            'comment': 'Analyst notes or remarks',
            'analyst_name': 'Person who handled the case'
        }
        return descriptions


def create_pdrm_firewall_templates():
    """Create template files for PDRM Firewall data"""
    formatter = PDRMFirewallFormatter()
    
    # Create CSV template
    csv_template_path = "EXPORT/pdrm_firewall_template.csv"
    formatter.create_csv_template(csv_template_path, include_sample_data=True)
    print(f"Created CSV template: {csv_template_path}")
    
    # Create JSON template
    sample_data = [formatter._create_sample_data()]
    json_template_path = "EXPORT/pdrm_firewall_template.json"
    formatter.export_to_json(sample_data, json_template_path)
    print(f"Created JSON template: {json_template_path}")
    
    # Create field documentation
    descriptions = formatter.get_field_descriptions()
    doc_path = "EXPORT/pdrm_firewall_fields.txt"
    with open(doc_path, 'w', encoding='utf-8') as f:
        f.write("PDRM Firewall Data Fields Documentation\n")
        f.write("=" * 50 + "\n\n")
        f.write("Required Fields (in exact order):\n\n")
        
        for i, field in enumerate(formatter.field_order, 1):
            description = descriptions.get(field, "No description available")
            derived_indicator = " [DERIVED]" if field in formatter.derived_fields else ""
            f.write(f"{i:2d}. {field}{derived_indicator}\n")
            f.write(f"    {description}\n\n")
    
    print(f"Created field documentation: {doc_path}")


if __name__ == "__main__":
    create_pdrm_firewall_templates()
