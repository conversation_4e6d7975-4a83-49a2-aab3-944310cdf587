#!/usr/bin/env python3
"""
Test script for new SOC Jira Tracker features

This script tests the new client configuration and PDRM Firewall data structure
without requiring actual Jira API calls.
"""

import json
import csv
from datetime import datetime
from client_config import ClientConfig, PDRMFirewallDataStructure
from pdrm_firewall_formatter import PDRMFirewall<PERSON><PERSON>atter
from data_validation import DataValidator


def test_client_configuration():
    """Test client configuration functionality"""
    print("=== Testing Client Configuration ===")
    
    # Test basic client operations
    print(f"Total clients: {len(ClientConfig.get_all_clients())}")
    print(f"MSOC clients: {len(ClientConfig.get_msoc_clients())}")
    print(f"Firewall clients: {len(ClientConfig.get_firewall_clients())}")
    
    # Test new clients
    new_clients = ["EPF Guardium", "TNGDigital", "NEM", "NXT", "PLUS", "NADI Tech", "EPF SIEM"]
    print(f"\nNew clients validation:")
    for client in new_clients:
        is_valid = ClientConfig.is_valid_client(client)
        print(f"  {client}: {'✓' if is_valid else '✗'}")
    
    # Test JQL generation
    jql_list = ClientConfig.get_jql_client_list()
    print(f"\nJQL client list length: {len(jql_list)}")
    has_quotes = '"' in jql_list
    print(f"Contains quoted clients: {has_quotes}")
    
    return True


def test_pdrm_firewall_structure():
    """Test PDRM Firewall data structure"""
    print("\n=== Testing PDRM Firewall Data Structure ===")
    
    # Test field order
    fields = PDRMFirewallDataStructure.get_field_order()
    print(f"Total fields: {len(fields)}")
    print(f"First field: {fields[0]}")
    print(f"Last field: {fields[-1]}")
    
    # Test derived fields
    derived = PDRMFirewallDataStructure.get_derived_fields()
    print(f"Derived fields: {len(derived)}")
    for field, formula in derived.items():
        print(f"  {field}: {formula}")
    
    # Test field mapping
    mapping = PDRMFirewallDataStructure.get_jira_mapping()
    print(f"Field mappings: {len(mapping)}")
    
    return True


def test_data_formatter():
    """Test PDRM Firewall data formatter"""
    print("\n=== Testing PDRM Firewall Formatter ===")
    
    # Create sample Jira data
    sample_jira_data = [
        {
            "Issue key": "MSOC-12345",
            "Summary": "[PDRM][MSOC-12345] Test Firewall Issue",
            "Custom field (PDRM Fault Category)": "Network Connectivity",
            "Custom field (Closing Time)": "2025-01-15 14:30:00",
            "Custom field (PDRM Acknowledge Venue)": "IPD Test Location",
            "Custom field (PDRM Acknowledge Time)": "2025-01-15 10:15:00",
            "Status": "Resolved",
            "Comment": "Test comment",
            "Custom field (Analyst Name)": "Test Analyst"
        }
    ]
    
    # Initialize formatter
    formatter = PDRMFirewallFormatter()
    
    # Transform data
    structured_data = formatter.transform_jira_data_to_pdrm_format(sample_jira_data)
    print(f"Transformed records: {len(structured_data)}")
    
    # Validate transformed data
    validation_results = formatter.validate_data(structured_data)
    print(f"Validation passed: {validation_results['valid']}")
    
    # Test CSV export
    test_csv_file = "EXPORT/test_pdrm_export.csv"
    formatter.export_to_csv(structured_data, test_csv_file)
    print(f"CSV export created: {test_csv_file}")
    
    # Test JSON export
    test_json_file = "EXPORT/test_pdrm_export.json"
    formatter.export_to_json(structured_data, test_json_file)
    print(f"JSON export created: {test_json_file}")
    
    return True


def test_data_validation():
    """Test data validation functionality"""
    print("\n=== Testing Data Validation ===")
    
    validator = DataValidator()
    
    # Test client configuration validation
    client_validation = validator.validate_client_configuration()
    print(f"Client config validation: {'✓' if client_validation['valid'] else '✗'}")
    
    # Test PDRM data validation with sample data
    sample_pdrm_data = [
        {
            "issue_key": "MSOC-12345",
            "summary": "[PDRM][MSOC-12345] Test Issue",
            "pdrm_fault_category": "Network",
            "closing_time": "2025-01-15 14:30:00",
            "pdrm_acknowledge_venue": "IPD Test",
            "tm_pdrm_holding_time": "=closing_time - pdrm_acknowledge_time",
            "total_time_taken": "=closing_time - issue_created_time",
            "pdrm_acknowledge_time": "2025-01-15 10:15:00",
            "pdrm_tl_holding_time": "=pdrm_acknowledge_time - tl_start_time",
            "states": "Resolved",
            "comment": "Test comment",
            "analyst_name": "Test Analyst"
        }
    ]
    
    pdrm_validation = validator.validate_pdrm_firewall_data(sample_pdrm_data)
    print(f"PDRM data validation: {'✓' if pdrm_validation['valid'] else '✗'}")
    print(f"Formula placeholders found: {len(pdrm_validation['derived_fields']['formula_placeholders'])}")
    
    return True


def test_file_generation():
    """Test that all template files were generated correctly"""
    print("\n=== Testing File Generation ===")
    
    files_to_check = [
        "EXPORT/pdrm_firewall_template.csv",
        "EXPORT/pdrm_firewall_template.json",
        "EXPORT/pdrm_firewall_fields.txt"
    ]
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"✓ {file_path} ({len(content)} chars)")
        except FileNotFoundError:
            print(f"✗ {file_path} (not found)")
            return False
    
    return True


def main():
    """Run all tests"""
    print("SOC Jira Tracker - New Features Test Suite")
    print("=" * 50)
    
    tests = [
        ("Client Configuration", test_client_configuration),
        ("PDRM Firewall Structure", test_pdrm_firewall_structure),
        ("Data Formatter", test_data_formatter),
        ("Data Validation", test_data_validation),
        ("File Generation", test_file_generation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"ERROR in {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! The new features are working correctly.")
        print("\nNext steps:")
        print("1. Review the generated template files in EXPORT/")
        print("2. Test with actual Jira data when ready")
        print("3. Use the new structured PDRM export in production")
    else:
        print("\n⚠️  Some tests failed. Please review the errors above.")
    
    return passed == len(results)


if __name__ == "__main__":
    main()
