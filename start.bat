@echo off
echo Starting SOC Jira Tracker...
echo Current directory: %CD%

REM Try to activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo No virtual environment found, using system Python
)

echo Running jira_main.py...
python jira_main.py

echo.
echo Process completed. Press any key to exit...
pause