# SOC Jira Tracker - Client Configuration & PDRM Firewall Data Structure

## Overview

This document describes the enhanced client configuration system and the new PDRM Firewall data structure implemented in the SOC Jira Tracker system.

## Client Configuration

### New Clients Added

The following clients have been added to the system:

1. **EPF Guardium** - Guardium-specific client
2. **TNGDigital** - Standard client
3. **NEM** - Standard client (part of NEM/NXT)
4. **NXT** - Standard client (part of NEM/NXT)
5. **PLUS** - Standard client
6. **NADI Tech** - Standard client
7. **EPF SIEM** - SIEM-specific client

### Complete Client List

| Client Name | Type | Description |
|-------------|------|-------------|
| MAG | Standard | Existing client |
| PDRM | Standard | Existing client |
| Astro | Standard | Existing client |
| Firefly | Standard | Existing client |
| EPF | Standard | Existing client |
| **EPF Guardium** | **Guardium** | **New - Guardium-specific** |
| UMWT | Standard | Existing client |
| UTP | Standard | Existing client (maintained for compatibility) |
| Jupem | Standard | Existing client |
| MIDF | Standard | Existing client |
| **TNGDigital** | **Standard** | **New client** |
| **NEM** | **Standard** | **New client** |
| **NXT** | **Standard** | **New client** |
| PDRM Firewall | Firewall | Existing firewall client |
| **PLUS** | **Standard** | **New client** |
| **NADI Tech** | **Standard** | **New client** |
| **EPF SIEM** | **SIEM** | **New - SIEM-specific** |

### Client Configuration Module

The new `client_config.py` module provides centralized client management:

```python
from client_config import ClientConfig

# Get all clients
all_clients = ClientConfig.get_all_clients()

# Get MSOC clients for JQL queries
msoc_clients = ClientConfig.get_msoc_clients()

# Get JQL-formatted client list
jql_client_list = ClientConfig.get_jql_client_list()

# Validate client names
validation = ClientConfig.validate_client_list(['MAG', 'PDRM', 'InvalidClient'])
```

## PDRM Firewall Data Structure

### Required Fields (Exact Order)

The PDRM Firewall data structure has been standardized with the following 12 fields in exact order:

1. **issue_key** - Jira issue ID (e.g., MSOC-12345)
2. **summary** - Short description of the issue
3. **pdrm_fault_category** - Classification of the fault type
4. **closing_time** - Timestamp when the ticket was closed
5. **pdrm_acknowledge_venue** - Venue acknowledged by PDRM
6. **tm_pdrm_holding_time** - Derived: closing_time - pdrm_acknowledge_time
7. **total_time_taken** - Derived: closing_time - issue_created_time
8. **pdrm_acknowledge_time** - Timestamp when PDRM acknowledged
9. **pdrm_tl_holding_time** - Derived: pdrm_acknowledge_time - tl_start_time
10. **states** - Current state or location of the issue
11. **comment** - Analyst notes or remarks
12. **analyst_name** - Person who handled the case

### Derived Fields with Formula Placeholders

Three fields are calculated/derived values:

- **tm_pdrm_holding_time**: `=closing_time - pdrm_acknowledge_time`
- **total_time_taken**: `=closing_time - issue_created_time`
- **pdrm_tl_holding_time**: `=pdrm_acknowledge_time - tl_start_time`

### Data Formats

#### CSV Format
```csv
issue_key,summary,pdrm_fault_category,closing_time,pdrm_acknowledge_venue,tm_pdrm_holding_time,total_time_taken,pdrm_acknowledge_time,pdrm_tl_holding_time,states,comment,analyst_name
MSOC-XXXXX,[PDRM][MSOC-XXXXX] Sample Issue,Network Connectivity,2025-01-15 14:30:00,IPD Sample Location,=closing_time - pdrm_acknowledge_time,=closing_time - issue_created_time,2025-01-15 10:15:00,=pdrm_acknowledge_time - tl_start_time,Resolved,Sample comment,Sample Analyst
```

#### JSON Format
```json
[
  {
    "issue_key": "MSOC-XXXXX",
    "summary": "[PDRM][MSOC-XXXXX] Sample Issue",
    "pdrm_fault_category": "Network Connectivity",
    "closing_time": "2025-01-15 14:30:00",
    "pdrm_acknowledge_venue": "IPD Sample Location",
    "tm_pdrm_holding_time": "=closing_time - pdrm_acknowledge_time",
    "total_time_taken": "=closing_time - issue_created_time",
    "pdrm_acknowledge_time": "2025-01-15 10:15:00",
    "pdrm_tl_holding_time": "=pdrm_acknowledge_time - tl_start_time",
    "states": "Resolved",
    "comment": "Sample comment",
    "analyst_name": "Sample Analyst"
  }
]
```

## Usage Examples

### Basic Client Operations

```python
from client_config import ClientConfig

# Check if a client is valid
if ClientConfig.is_valid_client("EPF Guardium"):
    print("Client is valid")

# Get clients by type
guardium_clients = ClientConfig.get_clients_by_type(ClientType.GUARDIUM)
siem_clients = ClientConfig.get_clients_by_type(ClientType.SIEM)
```

### PDRM Firewall Data Processing

```python
from pdrm_firewall_formatter import PDRMFirewallFormatter

# Initialize formatter
formatter = PDRMFirewallFormatter()

# Transform Jira data to PDRM format
structured_data = formatter.transform_jira_data_to_pdrm_format(jira_data)

# Export to CSV and JSON
formatter.export_to_csv(structured_data, "pdrm_firewall_data.csv")
formatter.export_to_json(structured_data, "pdrm_firewall_data.json")

# Validate data
validation_results = formatter.validate_data(structured_data)
```

### Data Validation

```python
from data_validation import DataValidator

# Initialize validator
validator = DataValidator()

# Validate client configuration
client_validation = validator.validate_client_configuration()

# Validate PDRM Firewall data
pdrm_validation = validator.validate_pdrm_firewall_data(data)

# Generate validation report
report = validator.generate_validation_report(pdrm_validation, "pdrm_firewall")
print(report)
```

## File Structure

```
soc_jira_tracker/
├── client_config.py              # Client configuration module
├── pdrm_firewall_formatter.py    # PDRM Firewall data formatter
├── data_validation.py            # Data validation functions
├── jira_main.py                  # Updated main application
├── EXPORT/
│   ├── pdrm_firewall_template.csv      # CSV template
│   ├── pdrm_firewall_template.json     # JSON template
│   ├── pdrm_firewall_fields.txt        # Field documentation
│   └── pdrm_firewall_structured_*.csv  # Generated structured files
└── README_CLIENT_CONFIG.md       # This documentation
```

## Maintenance

### Adding New Clients

1. Edit `client_config.py`
2. Add client to `ALL_CLIENTS` dictionary with appropriate type
3. Add to `MSOC_CLIENTS` list if it should be included in general exports
4. Run validation to ensure configuration is correct

### Modifying PDRM Firewall Structure

1. Update `PDRMFirewallDataStructure` class in `client_config.py`
2. Modify field mappings in `JIRA_FIELD_MAPPING`
3. Update derived field formulas in `DERIVED_FIELDS`
4. Regenerate templates using `pdrm_firewall_formatter.py`

### Validation

Always run validation after making changes:

```python
python client_config.py          # Show client summary
python data_validation.py        # Validate system configuration
python pdrm_firewall_formatter.py # Generate templates
```

## Migration Notes

- Existing functionality is preserved for backward compatibility
- UTP client is maintained in the configuration
- New structured PDRM export is available alongside existing export
- All new clients are automatically included in MSOC exports
