import os 

from pydrive.auth import GoogleAuth
from pydrive.drive import GoogleDrive
# from googleapiclient.http import MediaIo<PERSON>aseDownload
# from googleapiclient.errors import HttpE<PERSON>r


def upload_files_to_drive(file_paths:str , folder_id:str):
    """
    Uploads a list of files to a specified directory on Google Drive.

    Args:
    - file_paths (list): List of file paths to upload.
    - folder_id (str): ID of the destination folder on Google Drive.

    Returns:
    - uploaded_files (list): List of dictionaries containing information about the uploaded files.
    """

    gauth = GoogleAuth()
    gauth.LocalWebserverAuth()  # or gauth.CommandLineAuth() for command line authentication
    drive = GoogleDrive(gauth)

    uploaded_files = []

    # Upload each file to the specified directory
    for file_path in file_paths:
        # Extract file name from file path
        file_name = file_path.split('/')[-1]

        # Create file object
        file = drive.CreateFile({'title': file_name, 'parents': [{'id': folder_id}]})

        # Set file content and upload
        file.SetContentFile(file_path)
        file.Upload()

        # Add information about the uploaded file to the list
        uploaded_files.append({
            'title': file['title'],
            'id': file['id'],
            'webContentLink': file['webContentLink']  # Link to the uploaded file
        })

    return uploaded_files

